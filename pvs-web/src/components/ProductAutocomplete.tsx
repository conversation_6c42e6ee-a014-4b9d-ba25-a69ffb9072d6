import React, { useState, useEffect, useRef } from 'react';
import { productService, type ProductSearchResult } from '@/services';

interface ProductAutocompleteProps {
  value?: ProductSearchResult | null;
  onChange: (product: ProductSearchResult | null) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const ProductAutocomplete: React.FC<ProductAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Tìm kiếm sản phẩm...",
  disabled = false,
  className = ""
}) => {
  const [query, setQuery] = useState(value?.name || '');
  const [products, setProducts] = useState<ProductSearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Debounce search
  useEffect(() => {
    if (query.length < 3) {
      setProducts([]);
      setIsOpen(false);
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        setIsLoading(true);
        setError(null);
        const results = await productService.searchProducts(query, 10);
        setProducts(results);
        setIsOpen(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tìm kiếm');
        setProducts([]);
        setIsOpen(false);
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    
    // Clear selection if query doesn't match selected product
    if (value && newQuery !== value.name) {
      onChange(null);
    }
  };

  const handleProductSelect = (product: ProductSearchResult) => {
    setQuery(product.name);
    setIsOpen(false);
    onChange(product);
  };

  const handleInputFocus = () => {
    if (products.length > 0) {
      setIsOpen(true);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>

      {error && (
        <div className="mt-1 text-sm text-red-600">
          {error}
        </div>
      )}

      {isOpen && products.length > 0 && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {products.map((product) => (
            <div
              key={product.id}
              onClick={() => handleProductSelect(product)}
              className="px-3 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
            >
              <div className="font-medium text-gray-900">{product.name}</div>
              {product.description && (
                <div className="text-sm text-gray-500 truncate">
                  {product.description}
                </div>
              )}
              <div className="flex items-center gap-2 mt-1">
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  product.status === 'ACTIVE' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {product.status}
                </span>
                {product.is_favorite && (
                  <span className="text-yellow-500 text-xs">★</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {isOpen && products.length === 0 && !isLoading && query.length >= 3 && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
        >
          <div className="px-3 py-2 text-gray-500 text-center">
            Không tìm thấy sản phẩm nào
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductAutocomplete;
