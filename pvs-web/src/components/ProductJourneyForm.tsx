import React, { useState, useEffect } from 'react';
import {
  type ProductJourney,
  type ProductJourneyRequest,
  type ProductSearchResult
} from '../services';
import ProductAutocomplete from './ProductAutocomplete';

interface ProductJourneyFormProps {
  journey?: ProductJourney;
  onSubmit: (data: ProductJourneyRequest) => Promise<void>;
  onCancel: () => void;
}

const ProductJourneyForm: React.FC<ProductJourneyFormProps> = ({ journey, onSubmit, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<ProductSearchResult | null>(null);

  // Form state
  const [formData, setFormData] = useState<ProductJourneyRequest>({
    name: journey?.name || '',
    description: journey?.description || '',
    product_id: journey?.product_id || 0,
    flow_data: journey?.flow_data || JSON.stringify({
      components: {},
      actions: {},
      steps: [
        { id: "step1", title: "Nhận diện", actionIds: [] },
        { id: "step2", title: "Cân nhắc", actionIds: [] },
        { id: "step3", title: "Mua hàng", actionIds: [] },
        { id: "step4", title: "Sử dụng", actionIds: [] },
        { id: "step5", title: "Giữ chân", actionIds: [] },
      ]
    }),
    status: journey?.status || 'ACTIVE'
  });

  // Initialize selected product if editing
  useEffect(() => {
    if (journey && journey.product_id && journey.product_name) {
      setSelectedProduct({
        id: journey.product_id,
        name: journey.product_name,
        description: '',
        status: 'ACTIVE',
        is_favorite: false,
        created_at: '',
        updated_at: ''
      });
    }
  }, [journey]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleProductSelect = (product: ProductSearchResult | null) => {
    setSelectedProduct(product);
    setFormData(prev => ({
      ...prev,
      product_id: product?.id || 0
    }));
  };





  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await onSubmit(formData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save journey');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      <div className="row mb-3">
        <div className="col-md-6">
          <label htmlFor="name" className="form-label">Tên hành trình <span className="text-danger">*</span></label>
          <input
            type="text"
            className="form-control"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
          />
        </div>
        <div className="col-md-6">
          <label htmlFor="product_id" className="form-label">Sản phẩm <span className="text-danger">*</span></label>
          <ProductAutocomplete
            value={selectedProduct}
            onChange={handleProductSelect}
            placeholder="Tìm kiếm sản phẩm..."
            disabled={loading}
          />
          {formData.product_id === 0 && (
            <div className="text-danger small mt-1">Vui lòng chọn sản phẩm</div>
          )}
        </div>
      </div>

      <div className="mb-3">
        <label htmlFor="description" className="form-label">Mô tả</label>
        <textarea
          className="form-control"
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleInputChange}
        />
      </div>

      <div className="mb-3">
        <label htmlFor="status" className="form-label">Trạng thái</label>
        <select
          className="form-select"
          id="status"
          name="status"
          value={formData.status}
          onChange={handleInputChange}
        >
          <option value="ACTIVE">Hoạt động</option>
          <option value="INACTIVE">Không hoạt động</option>
          <option value="DRAFT">Bản nháp</option>
        </select>
      </div>





      {/* Flow Data JSON */}
      <div className="mb-4">
        <label htmlFor="flow_data" className="form-label">Dữ liệu luồng (JSON)</label>
        <textarea
          className="form-control font-monospace"
          id="flow_data"
          name="flow_data"
          rows={5}
          value={formData.flow_data}
          onChange={handleInputChange}
          placeholder='{"steps": [], "connections": []}'
        />
        <div className="form-text">
          Dữ liệu JSON mô tả luồng hành trình, có thể sử dụng để hiển thị diagram.
        </div>
      </div>

      <div className="d-flex justify-content-end gap-2">
        <button
          type="button"
          className="btn btn-secondary"
          onClick={onCancel}
          disabled={loading}
        >
          Hủy
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={loading}
        >
          {loading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Đang lưu...
            </>
          ) : journey ? 'Cập nhật hành trình' : 'Tạo hành trình'}
        </button>
      </div>
    </form>
  );
};

export default ProductJourneyForm;
