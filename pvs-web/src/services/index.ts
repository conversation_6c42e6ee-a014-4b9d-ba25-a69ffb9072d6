// Export shared utilities
export { apiClient } from './shared/apiClient';
export { BaseService } from './shared/BaseService';
export * from './shared/types';

// Export all services
export { authService } from './authService';
export { componentService } from './componentService';
export { productService } from './productService';
export { productJourneyService } from './productJourneyService';
export { mockAuthService } from './mockAuthService';

// Export service types
export type { LoginRequest, UserInfo, AuthResponse } from './authService';
export type {
  ProductInGroup,
  GroupWithProducts,
  GroupedProductsResponse,
  ProductFilters,
  ProductSearchResult
} from './productService';
export type {
  ProductJourney,
  ProductJourneyListItem,
  ProductJourneyRequest,
  ProductJourneyFilters
} from './productJourneyService';

// Default exports for backward compatibility
export { default as authServiceDefault } from './authService';
export { default as componentServiceDefault } from './componentService';
export { default as productServiceDefault } from './productService';
export { default as productJourneyServiceDefault } from './productJourneyService';
