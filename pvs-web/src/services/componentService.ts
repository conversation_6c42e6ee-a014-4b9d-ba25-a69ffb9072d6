import { BaseService } from './shared/BaseService';
import type { DropdownItem } from './shared/types';

class ComponentService extends BaseService {
  // Get components for dropdown
  async getComponentsForDropdown(search?: string): Promise<DropdownItem[]> {
    const filters = search ? { search } : {};
    const queryString = this.buildFilters(filters);
    return this.getWithSuccess<DropdownItem[]>(`/components/dropdown${queryString}`);
  }
}

// Create and export singleton instance
export const componentService = new ComponentService();
export default componentService;
