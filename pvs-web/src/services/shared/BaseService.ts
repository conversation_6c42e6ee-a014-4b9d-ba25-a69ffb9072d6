import { apiClient } from './apiClient';
import { type BaseFilters, ApiException } from './types';

export abstract class BaseService {
  protected client = apiClient;

  /**
   * Handle errors consistently across all services
   */
  protected handleError(error: any, context: string): never {
    console.error(`Error in ${context}:`, error);
    
    if (error instanceof ApiException) {
      throw error;
    }
    
    // Convert other errors to ApiException
    const message = error.response?.data?.message || error.message || 'An unexpected error occurred';
    const status = error.response?.status;
    const code = error.response?.data?.code;
    
    throw new ApiException(message, status, code);
  }

  /**
   * Build query parameters from filters object
   */
  protected buildFilters(filters: BaseFilters & Record<string, any>): string {
    return this.client.buildQueryParams(filters);
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(params: Record<string, any>, requiredFields: string[]): void {
    const missing = requiredFields.filter(field => 
      params[field] === undefined || params[field] === null || params[field] === ''
    );
    
    if (missing.length > 0) {
      throw new ApiException(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Validate ID parameter
   */
  protected validateId(id: number | string, fieldName: string = 'id'): void {
    if (!id || (typeof id === 'number' && id <= 0)) {
      throw new ApiException(`Invalid ${fieldName}: must be a positive number`);
    }
  }

  /**
   * Generic method for GET requests with pagination
   */
  protected async getPaginated<T>(
    endpoint: string, 
    filters: BaseFilters & Record<string, any> = {}
  ) {
    try {
      const queryString = this.buildFilters(filters);
      return await this.client.getPaginated<T>(`${endpoint}${queryString}`);
    } catch (error) {
      this.handleError(error, `getPaginated(${endpoint})`);
    }
  }

  /**
   * Generic method for GET requests with success check
   */
  protected async getWithSuccess<T>(endpoint: string) {
    try {
      return await this.client.getWithSuccessCheck<T>(endpoint);
    } catch (error) {
      this.handleError(error, `getWithSuccess(${endpoint})`);
    }
  }

  /**
   * Generic method for POST requests with success check
   */
  protected async postWithSuccess<T>(endpoint: string, data: any) {
    try {
      return await this.client.postWithSuccessCheck<T>(endpoint, data);
    } catch (error) {
      this.handleError(error, `postWithSuccess(${endpoint})`);
    }
  }

  /**
   * Generic method for PUT requests with success check
   */
  protected async putWithSuccess<T>(endpoint: string, data: any) {
    try {
      return await this.client.putWithSuccessCheck<T>(endpoint, data);
    } catch (error) {
      this.handleError(error, `putWithSuccess(${endpoint})`);
    }
  }

  /**
   * Generic method for PATCH requests with success check
   */
  protected async patchWithSuccess<T>(endpoint: string, data?: any) {
    try {
      return await this.client.patchWithSuccessCheck<T>(endpoint, data);
    } catch (error) {
      this.handleError(error, `patchWithSuccess(${endpoint})`);
    }
  }

  /**
   * Generic method for DELETE requests with success check
   */
  protected async deleteWithSuccess(endpoint: string) {
    try {
      return await this.client.deleteWithSuccessCheck(endpoint);
    } catch (error) {
      this.handleError(error, `deleteWithSuccess(${endpoint})`);
    }
  }

  /**
   * Generic method for toggle operations (favorite, status, etc.)
   */
  protected async toggleProperty(
    endpoint: string,
    id: number,
    property: string,
    value: boolean | string,
    context: string
  ): Promise<void> {
    this.validateId(id);

    try {
      const url = `${endpoint}/${id}/${property}?${property}=${value}`;
      await this.client.patchWithSuccessCheck<null>(url);
    } catch (error) {
      this.handleError(error, context);
    }
  }

  /**
   * Generic method for POST requests without auth token (for login, etc.)
   */
  protected async postWithoutAuth<T>(endpoint: string, data: any) {
    try {
      return await this.client.postWithoutAuthAndSuccessCheck<T>(endpoint, data);
    } catch (error) {
      this.handleError(error, `postWithoutAuth(${endpoint})`);
    }
  }
}
