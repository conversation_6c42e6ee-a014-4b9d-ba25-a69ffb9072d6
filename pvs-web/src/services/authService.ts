import { BaseService } from './shared/BaseService';
import { mockAuthService } from './mockAuthService';

const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface UserInfo {
  id: number;
  username: string;
  role_code: string;
  permissions: string[];
}

export interface AuthResponse {
  token: string;
  user: UserInfo;
}

class AuthService extends BaseService {
  async login(username: string, password: string): Promise<AuthResponse> {
    if (USE_MOCK) {
      return mockAuthService.login(username, password);
    }

    this.validateRequired({ username, password }, ['username', 'password']);
    return this.postWithoutAuth<AuthResponse>('/login', { username, password });
  }

  async refreshToken(token: string): Promise<AuthResponse> {
    if (USE_MOCK) {
      return mockAuthService.refreshToken(token);
    }

    this.validateRequired({ token }, ['token']);
    return this.postWithoutAuth<AuthResponse>('/refresh-token', { token });
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  getCurrentUser(): UserInfo | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default authService;