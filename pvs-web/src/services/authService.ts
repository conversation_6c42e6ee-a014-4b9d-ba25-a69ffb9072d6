import axios from 'axios';
import { mockAuthService } from './mockAuthService';
import { ApiException } from './shared/types';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';
const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface UserInfo {
  id: number;
  username: string;
  role_code: string;
  permissions: string[];
}

export interface AuthResponse {
  token: string;
  user: UserInfo;
}

class AuthService {
  private handleError(error: any, context: string): never {
    console.error(`Error in ${context}:`, error);

    const message = error.response?.data?.message || error.message || 'An unexpected error occurred';
    const status = error.response?.status;
    const code = error.response?.data?.code;

    throw new ApiException(message, status, code);
  }

  async login(username: string, password: string): Promise<AuthResponse> {
    if (USE_MOCK) {
      return mockAuthService.login(username, password);
    }

    try {
      const response = await axios.post(`${API_URL}/login`, {
        username,
        password
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new ApiException(response.data.message || 'Login failed');
    } catch (error) {
      this.handleError(error, 'login');
    }
  }

  async refreshToken(token: string): Promise<AuthResponse> {
    if (USE_MOCK) {
      return mockAuthService.refreshToken(token);
    }

    try {
      const response = await axios.post(`${API_URL}/refresh-token`, {
        token
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new ApiException(response.data.message || 'Token refresh failed');
    } catch (error) {
      this.handleError(error, 'refreshToken');
    }
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  getCurrentUser(): UserInfo | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

// Create and export singleton instance
export const authService = new AuthService();
