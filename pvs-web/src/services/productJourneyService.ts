import { BaseService } from './shared/BaseService';
import type { BaseFilters, PaginatedResponse } from './shared/types';

// Types specific to product journey service
export interface ProductJourneyComponent {
  id: number;
  component_id: number;
  component_name?: string;
}

export interface ProductJourney {
  id?: number;
  name: string;
  description: string;
  product_id: number;
  product_name?: string;
  flow_data: string;
  components?: ProductJourneyComponent[];
  status: string;
  created_at?: string;
  updated_at?: string;
}

export interface ProductJourneyListItem {
  id: number;
  name: string;
  description: string;
  product_id: number;
  product_name: string;
  status: string;
  component_count: number;
  created_at: string;
  updated_at: string;
}

export interface ProductJourneyRequest {
  name: string;
  description: string;
  product_id: number;
  flow_data: string;
  components: {
    component_id: number;
  }[];
  status: string;
}

export interface ProductJourneyFilters extends BaseFilters {
  product_id?: number;
  status?: string;
}

class ProductJourneyService extends BaseService {
  // Get product journeys with filters and pagination
  async getProductJourneys(filters: ProductJourneyFilters = {}): Promise<PaginatedResponse<ProductJourneyListItem>> {
    return this.getPaginated<ProductJourneyListItem>('/product-journeys', filters);
  }

  // Get product journey by ID
  async getProductJourneyById(id: number): Promise<ProductJourney> {
    this.validateId(id);
    return this.getWithSuccess<ProductJourney>(`/product-journeys/${id}`);
  }

  // Create new product journey
  async createProductJourney(journey: ProductJourneyRequest): Promise<ProductJourney> {
    this.validateRequired(journey, ['name', 'description', 'product_id', 'flow_data', 'status']);
    return this.postWithSuccess<ProductJourney>('/product-journeys', journey);
  }

  // Update product journey
  async updateProductJourney(id: number, journey: ProductJourneyRequest): Promise<ProductJourney> {
    this.validateId(id);
    this.validateRequired(journey, ['name', 'description', 'product_id', 'flow_data', 'status']);
    return this.putWithSuccess<ProductJourney>(`/product-journeys/${id}`, journey);
  }

  // Delete product journey
  async deleteProductJourney(id: number): Promise<void> {
    this.validateId(id);
    return this.deleteWithSuccess(`/product-journeys/${id}`);
  }
}

// Create and export singleton instance
export const productJourneyService = new ProductJourneyService();
export default productJourneyService;
