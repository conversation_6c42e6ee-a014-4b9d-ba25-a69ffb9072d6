import { BaseService } from './shared/BaseService';
import type { BaseFilters } from './shared/types';

// Types specific to product service
export interface ProductInGroup {
  id: number;
  name: string;
  description: string;
  current_stage: string;
  status: string;
  is_favorite: boolean;
  has_usage_data: boolean;
}

export interface GroupWithProducts {
  id: number;
  name: string;
  description: string;
  is_favorite: boolean;
  products: ProductInGroup[];
}

export interface GroupedProductsResponse {
  groups: GroupWithProducts[];
  count: number;
  total: number;
}

export interface ProductFilters extends BaseFilters {
  group_id?: number;
  year?: number;
  status?: string;
  is_favorite?: boolean;
  group_is_favorite?: boolean;
}

class ProductService extends BaseService {
  // Get grouped products with filters and pagination
  async getGroupedProducts(filters: ProductFilters = {}): Promise<GroupedProductsResponse> {
    try {
      // Extract group_is_favorite for client-side filtering
      const { group_is_favorite, ...serverFilters } = filters;

      const queryString = this.buildFilters(serverFilters);
      const response = await this.client.getWithSuccessCheck<GroupedProductsResponse>(
        `/products/grouped${queryString}`
      );

      // Apply client-side group_is_favorite filter
      if (group_is_favorite !== undefined) {
        response.groups = response.groups.filter(group => group.is_favorite === group_is_favorite);
      }

      return response;
    } catch (error) {
      this.handleError(error, 'getGroupedProducts');
    }
  }

  // Toggle product favorite status
  async toggleProductFavorite(productId: number, isFavorite: boolean): Promise<void> {
    return this.toggleProperty('/products', productId, 'favorite', isFavorite, 'toggleProductFavorite');
  }

  // Toggle product group favorite status
  async toggleProductGroupFavorite(groupId: number, isFavorite: boolean): Promise<void> {
    return this.toggleProperty('/product-groups', groupId, 'favorite', isFavorite, 'toggleProductGroupFavorite');
  }

  // Get product groups for filter dropdown
  async getProductGroups(): Promise<{ id: number; name: string; description: string }[]> {
    return this.getWithSuccess<{ id: number; name: string; description: string }[]>('/product-groups');
  }
}

// Create and export singleton instance
export const productService = new ProductService();
export default productService;
