# Services Architecture

## Overview

This directory contains all API services for the PVS web application. The services have been refactored to eliminate code duplication and provide a consistent, maintainable architecture.

## Structure

```
services/
├── shared/
│   ├── apiClient.ts      # Shared HTTP client with interceptors
│   ├── BaseService.ts    # Base class for all services
│   └── types.ts          # Common types and interfaces
├── authService.ts        # Authentication service
├── componentService.ts   # Component management service
├── productService.ts     # Product management service
├── productJourneyService.ts # Product journey service
├── mockAuthService.ts    # Mock authentication for development
├── index.ts              # Barrel export file
└── README.md            # This file
```

## Shared Components

### ApiClient (`shared/apiClient.ts`)

A singleton HTTP client built on Axios with:
- Automatic authentication token injection
- Response/request interceptors
- Centralized error handling
- Helper methods for common API patterns

```typescript
import { apiClient } from './shared/apiClient';

// Direct usage
const data = await apiClient.get('/endpoint');

// With success check
const data = await apiClient.getWithSuccessCheck('/endpoint');

// Paginated requests
const response = await apiClient.getPaginated('/endpoint');
```

### BaseService (`shared/BaseService.ts`)

Abstract base class providing:
- Consistent error handling
- Parameter validation
- Common CRUD operations
- Filter building utilities

```typescript
class MyService extends BaseService {
  async getItems(filters = {}) {
    return this.getPaginated('/items', filters);
  }
  
  async getItem(id: number) {
    this.validateId(id);
    return this.getWithSuccess(`/items/${id}`);
  }
}
```

### Types (`shared/types.ts`)

Common interfaces and types:
- `ApiResponse<T>` - Standard API response wrapper
- `PaginatedResponse<T>` - Paginated response format
- `BaseFilters` - Common filter parameters
- `DropdownItem` - Standard dropdown item format
- `ApiException` - Custom error class

## Services

### AuthService
- Handles login/logout functionality
- Supports both real and mock authentication
- Manages user session and tokens

### ProductService
- Manages product and product group operations
- Handles favorite toggling
- Supports filtering and pagination

### ComponentService
- Simple service for component dropdown data
- Demonstrates minimal service implementation

### ProductJourneyService
- Full CRUD operations for product journeys
- Comprehensive filtering and pagination
- Input validation

## Usage

### Import Services

```typescript
// Import specific services
import { productService, authService } from '@/services';

// Import types
import type { ProductFilters, UserInfo } from '@/services';

// Import shared utilities
import { apiClient, BaseService } from '@/services';
```

### Error Handling

All services use consistent error handling through `ApiException`:

```typescript
try {
  const data = await productService.getGroupedProducts();
} catch (error) {
  if (error instanceof ApiException) {
    console.error('API Error:', error.message, error.status);
  }
}
```

### Creating New Services

1. Extend `BaseService` for consistent behavior:

```typescript
import { BaseService } from './shared/BaseService';
import type { BaseFilters } from './shared/types';

interface MyFilters extends BaseFilters {
  customField?: string;
}

class MyService extends BaseService {
  async getItems(filters: MyFilters = {}) {
    return this.getPaginated<MyItem>('/my-items', filters);
  }
  
  async createItem(data: CreateItemRequest) {
    this.validateRequired(data, ['name', 'description']);
    return this.postWithSuccess<MyItem>('/my-items', data);
  }
}

export const myService = new MyService();
```

2. Add exports to `index.ts`
3. Update this README if needed

## Benefits

- **Reduced Code Duplication**: Common patterns extracted to shared components
- **Consistent Error Handling**: All services use the same error handling approach
- **Type Safety**: Comprehensive TypeScript types throughout
- **Maintainability**: Changes to common functionality only need to be made once
- **Testability**: Services can be easily mocked and tested
- **Scalability**: Easy to add new services following established patterns

## Migration Notes

The refactoring maintains backward compatibility:
- All existing service exports remain the same
- Method signatures are unchanged
- Error handling is improved but maintains the same interface

Existing code should continue to work without changes.
