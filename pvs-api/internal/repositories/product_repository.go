package repositories

import (
	"context"
	"time"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type ProductRepository interface {
	Create(ctx context.Context, product *models.Product) error
	CreateStatusLog(ctx context.Context, log *models.ProductStatusLog) error
	GetByID(ctx context.Context, id uint) (*models.Product, error)
	List(ctx context.Context, params *utils.Pagination) ([]*models.Product, int64, error)
	Search(ctx context.Context, query string, limit int) ([]*models.Product, error)
	Update(ctx context.Context, product *models.Product) error
	UpdateStatus(ctx context.Context, id uint, status, changedBy, note string) error
	UpdateFavoriteStatus(ctx context.Context, id uint, isFavorite bool) error
	Delete(ctx context.Context, id uint) error
	ExistByID(ctx context.Context, id uint) (bool, error)
	ExistsInGroup(ctx context.Context, id uint, groupID uint) (bool, error)
	AddProductToGroup(ctx context.Context, productID, groupID uint) error
	RemoveProductFromGroup(ctx context.Context, productID, groupID uint) error
	AddComponentToProduct(ctx context.Context, productID, componentID uint) error
	RemoveComponentFromProduct(ctx context.Context, productID, componentID uint) error
	HasComponent(ctx context.Context, productID uint, componentID uint) (bool, error)
	ListProductGroups(ctx context.Context, productID uint) ([]*models.ProductGroup, error)
	ListComponents(ctx context.Context, productID uint) ([]*models.Component, error)
	ListStages(ctx context.Context, productID uint) ([]*models.ProductStage, error)
	ListStatusLogs(ctx context.Context, productID uint) ([]*models.ProductStatusLog, error)
	SetProductStage(ctx context.Context, productID uint, stageCode string, startDate time.Time, notes string) (*models.ProductStage, error)
	ListGroupedProducts(ctx context.Context, filters map[string]interface{}, params *utils.Pagination) (*dto.GroupedProductsResponse, error)
}

type productRepository struct {
	db *gorm.DB
}

func NewProductRepository(db *gorm.DB) ProductRepository {
	return &productRepository{db: db}
}

func (r *productRepository) Create(ctx context.Context, product *models.Product) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {

		// Create the product
		if err := tx.Create(product).Error; err != nil {
			return err
		}

		// Create the initial status log
		statusLog := &models.ProductStatusLog{
			ProductID: product.ID,
			Status:    product.Status,
			ChangedBy: "system", // or get from auth context
			ChangedAt: time.Now(),
			Note:      "Initial product status",
		}

		if err := tx.Create(statusLog).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *productRepository) GetByID(ctx context.Context, id uint) (*models.Product, error) {
	var product models.Product

	err := r.db.WithContext(ctx).
		Preload("ProductGroups").
		Preload("Components").
		First(&product, id).Error

	if err != nil {
		return nil, err
	}

	return &product, nil
}

func (r *productRepository) ExistByID(ctx context.Context, id uint) (bool, error) {
	var exists bool
	err := r.db.WithContext(ctx).Model(&models.Product{}).Select("count(*) > 0").Where("id = ?", id).Find(&exists).Error
	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *productRepository) ExistsInGroup(ctx context.Context, id uint, groupID uint) (bool, error) {
	var exists bool
	err := r.db.WithContext(ctx).
		Model(&models.ProductProductGroup{}).
		Select("count(*) > 0").
		Where("product_id = ? AND product_group_id = ?", id, groupID).
		Find(&exists).Error
	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *productRepository) List(ctx context.Context, params *utils.Pagination) ([]*models.Product, int64, error) {
	var products []*models.Product
	var count int64

	query := r.db.WithContext(ctx).Model(&models.Product{})

	// Apply filters if any
	if params.Filters != nil {
		if isActive, ok := params.Filters["is_active"].(bool); ok {
			query = query.Where("is_active = ?", isActive)
		}

		if groupID, ok := params.Filters["product_group_id"].(uint); ok {
			query = query.Joins("JOIN product_product_groups ON product_product_groups.product_id = products.id").
				Where("product_product_groups.product_group_id = ?", groupID)
		}
	}

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	err := query.
		Scopes(utils.Paginate(params)).
		Preload("ProductGroups").
		Preload("ProductStages").
		Find(&products).Error

	if err != nil {
		return nil, 0, err
	}

	return products, count, nil
}

func (r *productRepository) Search(ctx context.Context, query string, limit int) ([]*models.Product, error) {
	var products []*models.Product

	// Search in name and description fields
	searchQuery := r.db.WithContext(ctx).Model(&models.Product{}).
		Where("name ILIKE ? OR description ILIKE ?", "%"+query+"%", "%"+query+"%").
		Order("name ASC").
		Limit(limit)

	err := searchQuery.Find(&products).Error
	if err != nil {
		return nil, err
	}

	return products, nil
}

func (r *productRepository) Update(ctx context.Context, product *models.Product) error {
	return r.db.WithContext(ctx).Model(product).Updates(product).Error
}

func (r *productRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.Product{}, id).Error
}

func (r *productRepository) AddProductToGroup(ctx context.Context, productID, groupID uint) error {
	productProductGroup := models.ProductProductGroup{
		ProductID:      productID,
		ProductGroupID: groupID,
	}

	return r.db.WithContext(ctx).Create(&productProductGroup).Error
}

func (r *productRepository) RemoveProductFromGroup(ctx context.Context, productID, groupID uint) error {
	return r.db.WithContext(ctx).
		Where("product_id = ? AND product_group_id = ?", productID, groupID).
		Delete(&models.ProductProductGroup{}).Error
}

func (r *productRepository) AddComponentToProduct(ctx context.Context, productID, componentID uint) error {
	productComponent := models.ProductComponent{
		ProductID:   productID,
		ComponentID: componentID,
	}

	return r.db.WithContext(ctx).Create(&productComponent).Error
}

func (r *productRepository) RemoveComponentFromProduct(ctx context.Context, productID, componentID uint) error {
	return r.db.WithContext(ctx).
		Where("product_id = ? AND component_id = ?", productID, componentID).
		Delete(&models.ProductComponent{}).Error
}

func (r *productRepository) ListProductGroups(ctx context.Context, productID uint) ([]*models.ProductGroup, error) {
	var productGroups []*models.ProductGroup

	err := r.db.WithContext(ctx).
		Joins("JOIN product_product_groups ON product_product_groups.product_group_id = product_groups.id").
		Where("product_product_groups.product_id = ?", productID).
		Find(&productGroups).Error

	if err != nil {
		return nil, err
	}

	return productGroups, nil
}

func (r *productRepository) ListComponents(ctx context.Context, productID uint) ([]*models.Component, error) {
	var components []*models.Component

	err := r.db.WithContext(ctx).
		Joins("JOIN product_components ON product_components.component_id = components.id").
		Where("product_components.product_id = ?", productID).
		Find(&components).Error

	if err != nil {
		return nil, err
	}

	return components, nil
}

func (r *productRepository) HasComponent(ctx context.Context, productID uint, componentID uint) (bool, error) {
	var exists bool
	err := r.db.WithContext(ctx).
		Model(&models.ProductComponent{}).
		Select("count(*) > 0").
		Where("product_id = ? AND component_id = ?", productID, componentID).
		Find(&exists).Error
	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *productRepository) ListStages(ctx context.Context, productID uint) ([]*models.ProductStage, error) {
	var stages []*models.ProductStage

	err := r.db.WithContext(ctx).
		Joins("JOIN product_stages ON product_stages.product_id = products.id").
		Where("product_stages.product_id = ?", productID).
		Find(&stages).Error

	if err != nil {
		return nil, err
	}

	return stages, nil
}

func (r *productRepository) CreateStatusLog(ctx context.Context, log *models.ProductStatusLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

func (r *productRepository) UpdateStatus(ctx context.Context, id uint, status, changedBy, note string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update product status
		if err := tx.Model(&models.Product{}).Where("id = ?", id).Update("status", status).Error; err != nil {
			return err
		}

		// Create status log
		statusLog := &models.ProductStatusLog{
			ProductID: id,
			Status:    status,
			ChangedBy: changedBy,
			ChangedAt: time.Now(),
			Note:      note,
		}

		if err := tx.Create(statusLog).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *productRepository) UpdateFavoriteStatus(ctx context.Context, id uint, isFavorite bool) error {
	return r.db.WithContext(ctx).Model(&models.Product{}).Where("id = ?", id).Update("is_favorite", isFavorite).Error
}

func (r *productRepository) ListStatusLogs(ctx context.Context, productID uint) ([]*models.ProductStatusLog, error) {
	var logs []*models.ProductStatusLog
	var count int64

	query := r.db.WithContext(ctx).Model(&models.ProductStatusLog{}).Where("product_id = ?", productID)

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, err
	}

	// Apply pagination and ordering
	err := query.
		Order("changed_at DESC").
		Find(&logs).Error

	if err != nil {
		return nil, err
	}

	return logs, nil
}

func (r *productRepository) SetProductStage(ctx context.Context, productID uint, stageCode string, startDate time.Time, notes string) (*models.ProductStage, error) {
	var newStage *models.ProductStage

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get current product
		var product models.Product
		if err := tx.First(&product, productID).Error; err != nil {
			return err
		}

		// Create new stage
		newStage = &models.ProductStage{
			ProductID: productID,
			StageCode: stageCode,
			StartDate: startDate,
			Notes:     notes,
		}
		if err := tx.Create(newStage).Error; err != nil {
			return err
		}

		// Close old stage if exists
		if product.StageID != nil {
			if err := tx.Model(&models.ProductStage{}).
				Where("id = ?", product.StageID).
				Update("end_date", startDate).Error; err != nil {
				return err
			}
		}

		// Update product's current stage
		if err := tx.Model(&product).Update("stage_id", newStage.ID).Error; err != nil {
			return err
		}

		return nil
	})

	return newStage, err
}

func (r *productRepository) ListGroupedProducts(ctx context.Context, filters map[string]interface{}, params *utils.Pagination) (*dto.GroupedProductsResponse, error) {
	var groups []models.ProductGroup
	var response dto.GroupedProductsResponse

	// Initialize Groups as empty slice to avoid null in JSON response
	response.Groups = []dto.GroupWithProducts{}

	// Base query to get all groups
	groupQuery := r.db.WithContext(ctx).Model(&models.ProductGroup{})

	// Get all groups first
	if err := groupQuery.Find(&groups).Error; err != nil {
		return nil, err
	}

	// Base product query
	productQuery := r.db.WithContext(ctx).Model(&models.Product{}).
		Joins("LEFT JOIN product_stages ON product_stages.id = products.stage_id")

	// Apply filters
	productQuery = productQuery.Joins("JOIN product_product_groups ON products.id = product_product_groups.product_id")

	if groupID, ok := filters["group_id"].(uint); ok && groupID > 0 {
		productQuery = productQuery.Where("product_product_groups.product_group_id = ?", groupID)
	}

	// Add component filter if provided
	if componentID, ok := filters["component_id"].(uint); ok && componentID > 0 {
		productQuery = productQuery.
			Joins("JOIN product_components ON products.id = product_components.product_id").
			Where("product_components.component_id = ?", componentID)
	}

	// Tách các điều kiện lọc thành các phần riêng biệt để tránh cached plan error
	if status, ok := filters["status"].(string); ok && status != "" {
		productQuery = productQuery.Where("products.status = ?", status)
	}

	if isFavorite, ok := filters["is_favorite"].(bool); ok {
		productQuery = productQuery.Where("products.is_favorite = ?", isFavorite)
	}

	if year, ok := filters["year"].(uint); ok && year > 0 {
		// Sử dụng subquery thay vì EXISTS để tránh cached plan error
		productQuery = productQuery.Where(`
			products.id IN (
				SELECT DISTINCT product_id FROM product_status_logs 
				WHERE date_part('year', changed_at) <= ?
			)`, year)

		// Nếu có cả status và year, thêm điều kiện lọc status trong subquery
		if status, ok := filters["status"].(string); ok && status != "" {
			productQuery = productQuery.Where(`
				products.id IN (
					SELECT DISTINCT product_id FROM product_status_logs 
					WHERE date_part('year', changed_at) <= ? AND status = ?
				)`, year, status)
		}
	}

	// Calculate total products before pagination
	var total int64
	if err := productQuery.Count(&total).Error; err != nil {
		return nil, err
	}
	response.Total = total

	// Apply pagination to the base query
	productQuery = productQuery.Scopes(utils.Paginate(params))

	// Get all paginated products and their group IDs
	var productsWithGroups []dto.ProductWithGroup

	// Sử dụng cấu trúc SELECT cố định để tránh cached plan error
	err := productQuery.Select(`
		products.id,
		products.name,
		products.description,
		product_stages.stage_code as current_stage,
		products.status,
		products.is_favorite,
		product_product_groups.product_group_id as group_id,
		(SELECT COUNT(*) > 0 FROM product_usage_stats WHERE product_usage_stats.product_id = products.id) as has_usage_data
	`).
		Order("product_product_groups.product_group_id, products.name ASC").
		Find(&productsWithGroups).Error
	if err != nil {
		return nil, err
	}

	// Create a map to store products by group
	groupProducts := make(map[uint][]dto.ProductInGroup)

	// Organize products by group
	for _, p := range productsWithGroups {
		product := dto.ProductInGroup{
			ID:           p.ID,
			Name:         p.Name,
			Description:  p.Description,
			CurrentStage: p.CurrentStage,
			Status:       p.Status,
			IsFavorite:   p.IsFavorite,
			HasUsageData: p.HasUsageData,
		}
		groupProducts[p.GroupID] = append(groupProducts[p.GroupID], product)
	}

	// Build final response - always include all groups, even if empty
	for _, group := range groups {
		products, exists := groupProducts[group.ID]
		if !exists {
			products = []dto.ProductInGroup{} // Empty slice instead of nil
		}
		groupWithProducts := dto.GroupWithProducts{
			ID:          group.ID,
			Name:        group.Name,
			Description: group.Description,
			IsFavorite:  group.IsFavorite,
			Products:    products,
		}
		response.Groups = append(response.Groups, groupWithProducts)
	}

	response.Count = int64(len(productsWithGroups))

	return &response, nil
}
