package dto

import (
	"pvs-api/internal/models"
	"time"
)

// ProductJourneyRequest định nghĩa DTO cho việc tạo/cập nhật ProductJourney
type ProductJourneyRequest struct {
	Name        string `json:"name" binding:"required" example:"Hành trình mở tài khoản"`
	Description string `json:"description" example:"Hành trình khách hàng mở tài khoản ngân hàng"`
	ProductID   uint   `json:"product_id" binding:"required" example:"1"`
	FlowData    string `json:"flow_data" example:"{\"components\": {...}, \"actions\": {...}, \"steps\": [...]}"`
	Status      string `json:"status" example:"ACTIVE"`
}

// ProductJourneyResponse định nghĩa DTO cho response ProductJourney
type ProductJourneyResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	ProductID   uint      `json:"product_id"`
	ProductName string    `json:"product_name,omitempty"`
	FlowData    string    `json:"flow_data"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ProductJourneyListResponse định nghĩa DTO cho danh sách hành trình
type ProductJourneyListResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	ProductID   uint      `json:"product_id"`
	ProductName string    `json:"product_name"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToProductJourneyModel chuyển đổi ProductJourneyRequest sang model ProductJourney
func (req *ProductJourneyRequest) ToProductJourneyModel() *models.ProductJourney {
	journey := &models.ProductJourney{
		Name:        req.Name,
		Description: req.Description,
		ProductID:   req.ProductID,
		FlowData:    req.FlowData,
		Status:      req.Status,
	}

	if journey.Status == "" {
		journey.Status = "ACTIVE"
	}

	return journey
}

// FromProductJourneyModel chuyển đổi model ProductJourney sang ProductJourneyResponse
func FromProductJourneyModel(journey *models.ProductJourney) *ProductJourneyResponse {
	response := &ProductJourneyResponse{
		ID:          journey.ID,
		Name:        journey.Name,
		Description: journey.Description,
		ProductID:   journey.ProductID,
		FlowData:    journey.FlowData,
		Status:      journey.Status,
		CreatedAt:   journey.CreatedAt,
		UpdatedAt:   journey.UpdatedAt,
	}

	if journey.Product != nil {
		response.ProductName = journey.Product.Name
	}

	return response
}

// FromProductJourneyModelList chuyển đổi danh sách model ProductJourney sang ProductJourneyListResponse
func FromProductJourneyModelList(journey *models.ProductJourney) *ProductJourneyListResponse {
	response := &ProductJourneyListResponse{
		ID:          journey.ID,
		Name:        journey.Name,
		Description: journey.Description,
		ProductID:   journey.ProductID,
		Status:      journey.Status,
		CreatedAt:   journey.CreatedAt,
		UpdatedAt:   journey.UpdatedAt,
	}

	if journey.Product != nil {
		response.ProductName = journey.Product.Name
	}

	return response
}
