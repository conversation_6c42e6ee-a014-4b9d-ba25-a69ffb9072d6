package controllers

import (
	"net/http"
	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RoleController struct {
	roleRepo repositories.RoleRepository
}

func NewRoleController(db *gorm.DB) *RoleController {
	return &RoleController{
		roleRepo: repositories.NewRoleRepository(db),
	}
}

// CreateRole godoc
// @Summary      Tạo vai trò mới
// @Description  Tạo một vai trò mới với thông tin được cung cấp
// @Tags         Roles
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        role  body      dto.RoleRequest  true  "Thông tin vai trò"
// @Success      201  {object}  dto.RoleResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /roles [post]
func (c *RoleController) CreateRole(ctx *gin.Context) {
	var request dto.RoleRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra permissions có hợp lệ không
	for _, perm := range request.Permissions {
		if !isValidPermission(perm) {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid permission: "+perm, "")
			return
		}
	}

	role := &models.Role{
		Code:        request.Code,
		Name:        request.Name,
		Description: request.Description,
		Permissions: request.Permissions,
	}

	if err := c.roleRepo.Create(ctx, role); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create role", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "Role created successfully", dto.FromRoleModel(role))
}

// UpdateRolePermissions godoc
// @Summary      Cập nhật quyền cho vai trò
// @Description  Cập nhật danh sách quyền cho một vai trò theo mã
// @Tags         Roles
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        code  path      string  true  "Mã vai trò"
// @Param        permissions  body  dto.UpdateRolePermissionsRequest  true  "Danh sách quyền mới"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      403  {object}  utils.Response "Forbidden - Cannot modify system role"
// @Failure      404  {object}  utils.Response "Not Found"
// @Failure      500  {object}  utils.Response
// @Router       /roles/{code}/permissions [patch]
func (c *RoleController) UpdateRolePermissions(ctx *gin.Context) {
	roleCode := ctx.Param("code")

	var request dto.UpdateRolePermissionsRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra role có tồn tại và không phải system role
	role, err := c.roleRepo.GetByCode(ctx, roleCode)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Role not found", err.Error())
		return
	}

	if role.IsSystem {
		utils.ErrorResponse(ctx, http.StatusForbidden, "Cannot modify system role", "")
		return
	}

	// Kiểm tra permissions có hợp lệ không
	for _, perm := range request.Permissions {
		if !isValidPermission(perm) {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid permission: "+perm, "")
			return
		}
	}

	if err := c.roleRepo.UpdatePermissions(ctx, roleCode, request.Permissions); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update role permissions", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Role permissions updated successfully", nil)
}

// ListRoles godoc
// @Summary      Lấy danh sách vai trò
// @Description  Trả về danh sách tất cả vai trò có phân trang
// @Tags         Roles
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page      query    int     false  "Số trang"
// @Param        limit     query    int     false  "Số lượng mỗi trang"
// @Success      200  {object}  utils.Response{data=[]dto.RoleResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /roles [get]
func (c *RoleController) ListRoles(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	roles, total, err := c.roleRepo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list roles", err.Error())
		return
	}

	// Initialize as empty slice to ensure JSON returns [] instead of null
	response := make([]dto.RoleResponse, 0)
	for _, role := range roles {
		response = append(response, *dto.FromRoleModel(role))
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Roles retrieved successfully", response, total)
}

// GetRoleByCode godoc
// @Summary      Lấy thông tin vai trò theo mã
// @Description  Trả về thông tin chi tiết của một vai trò theo mã
// @Tags         Roles
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        code  path      string  true  "Mã vai trò"
// @Success      200  {object}  dto.RoleResponse
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response "Not Found"
// @Router       /roles/{code} [get]
func (c *RoleController) GetRoleByCode(ctx *gin.Context) {
	code := ctx.Param("code")

	role, err := c.roleRepo.GetByCode(ctx, code)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Role not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Role retrieved successfully", dto.FromRoleModel(role))
}

// UpdateRole godoc
// @Summary      Cập nhật thông tin vai trò
// @Description  Cập nhật thông tin của một vai trò theo mã
// @Tags         Roles
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        code  path      string  true  "Mã vai trò"
// @Param        role  body      dto.RoleRequest  true  "Thông tin vai trò"
// @Success      200  {object}  dto.RoleResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      403  {object}  utils.Response "Forbidden - Cannot modify system role"
// @Failure      404  {object}  utils.Response "Not Found"
// @Failure      500  {object}  utils.Response
// @Router       /roles/{code} [put]
func (c *RoleController) UpdateRole(ctx *gin.Context) {
	code := ctx.Param("code")

	// Kiểm tra role có tồn tại
	existingRole, err := c.roleRepo.GetByCode(ctx, code)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Role not found", err.Error())
		return
	}

	// Kiểm tra có phải system role không
	if existingRole.IsSystem {
		utils.ErrorResponse(ctx, http.StatusForbidden, "Cannot modify system role", "")
		return
	}

	var request dto.RoleRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra permissions có hợp lệ không
	for _, perm := range request.Permissions {
		if !isValidPermission(perm) {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid permission: "+perm, "")
			return
		}
	}

	// Cập nhật thông tin role
	existingRole.Name = request.Name
	existingRole.Description = request.Description
	existingRole.Permissions = request.Permissions

	if err := c.roleRepo.Update(ctx, existingRole); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update role", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Role updated successfully", dto.FromRoleModel(existingRole))
}

// DeleteRole godoc
// @Summary      Xóa vai trò
// @Description  Xóa một vai trò theo mã
// @Tags         Roles
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        code  path      string  true  "Mã vai trò"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response "Bad Request - Role is assigned to users"
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      403  {object}  utils.Response "Forbidden - Cannot delete system role"
// @Failure      404  {object}  utils.Response "Not Found"
// @Failure      500  {object}  utils.Response
// @Router       /roles/{code} [delete]
func (c *RoleController) DeleteRole(ctx *gin.Context) {
	code := ctx.Param("code")

	// Kiểm tra role có tồn tại
	existingRole, err := c.roleRepo.GetByCode(ctx, code)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Role not found", err.Error())
		return
	}

	// Kiểm tra có phải system role không
	if existingRole.IsSystem {
		utils.ErrorResponse(ctx, http.StatusForbidden, "Cannot delete system role", "")
		return
	}

	// Kiểm tra xem role có đang được sử dụng không
	if len(existingRole.Users) > 0 {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Cannot delete role that is assigned to users", "")
		return
	}

	if err := c.roleRepo.Delete(ctx, code); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete role", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Role deleted successfully", nil)
}

// isValidPermission kiểm tra xem permission có hợp lệ không
func isValidPermission(permission string) bool {
	// Kiểm tra trong danh sách tất cả các permission
	allPerms := models.AllPermissions()
	for _, p := range allPerms {
		if p == permission {
			return true
		}
	}
	return false
	// TODO: Update to use slices.Contains when Go 1.21+ is available
}
