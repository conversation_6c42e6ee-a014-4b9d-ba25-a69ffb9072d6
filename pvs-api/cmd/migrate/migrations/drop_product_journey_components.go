package migrations

import (
	"fmt"
	"gorm.io/gorm"
)

// DropProductJourneyComponents drops the product_journey_components table
// since ProductJourney no longer has direct relationship with Components
func DropProductJourneyComponents(db *gorm.DB) error {
	fmt.Println("Dropping product_journey_components table...")

	// Check if table exists
	if !db.Migrator().HasTable("product_journey_components") {
		fmt.Println("product_journey_components table does not exist, skipping...")
		return nil
	}

	// Drop the table
	if err := db.Migrator().DropTable("product_journey_components"); err != nil {
		return fmt.Errorf("failed to drop product_journey_components table: %w", err)
	}

	fmt.Println("Successfully dropped product_journey_components table")
	return nil
}
