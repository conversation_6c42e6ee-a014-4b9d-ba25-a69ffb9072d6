package utils

import (
	"github.com/gin-gonic/gin"
)

// Response là cấu trúc phản hồi chuẩn cho API
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   interface{} `json:"error,omitempty"`
	Count   int         `json:"count,omitempty"`
	Total   int64       `json:"total"`
}

// SuccessResponse trả về phản hồi thành công
func SuccessResponse(c *gin.Context, statusCode int, message string, data interface{}) {
	c.J<PERSON>(statusCode, Response{
		Success: true,
		Message: message,
		Data:    data,
	})
}

func SuccessPaggingResponse[T any](c *gin.Context, statusCode int, message string, data []T, total int64) {
	c.JSON(statusCode, Response{
		Success: true,
		Message: message,
		Data:    data,
		Count:   len(data),
		Total:   total,
	})
}

// ErrorResponse trả về phản hồi lỗi
func ErrorResponse(c *gin.Context, statusCode int, message string, err interface{}) {
	c.JSON(statusCode, Response{
		Success: false,
		Message: message,
		Error:   err,
	})
}
